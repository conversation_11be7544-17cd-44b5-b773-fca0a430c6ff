import React, { useEffect, useState } from "react";
import { useSearchParams, Link, useNavigate } from "react-router-dom";
import { GlobalContext, showToast } from "Src/globalContext";
import { AuthContext } from "Src/authContext";
import MkdSDK from "Utils/MkdSDK";

const InvoiceClientSuccessPage = () => {
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const { state: authState } = React.useContext(AuthContext);
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [paymentData, setPaymentData] = useState(null);
  const [error, setError] = useState(null);

  // Get session_id from URL parameters
  const sessionId = searchParams.get("session_id");

  useEffect(() => {
    const fetchPaymentDetails = async () => {
      if (!sessionId) {
        setError("No session ID found");
        setLoading(false);
        return;
      }

      try {
        const sdk = new MkdSDK();
        // You might need to create an endpoint to verify the checkout session
        // For now, we'll just show a success message
        setPaymentData({ sessionId });
        setLoading(false);
      } catch (err) {
        console.error("Error fetching payment details:", err);
        setError("Failed to verify payment");
        setLoading(false);
      }
    };

    fetchPaymentDetails();
  }, [sessionId]);

  const handleViewInvoices = () => {
    // Check if user is logged in
    if (!authState.isLoggedIn || !authState.token) {
      // If not logged in, redirect to login
      window.location.href = "/login";
    } else {
      navigate("/client/invoices");
    }
  };

  const handleViewDashboard = () => {
    // Check if user is logged in
    if (!authState.isLoggedIn || !authState.token) {
      // If not logged in, redirect to login
      window.location.href = "/login";
    } else {
      navigate("/client/projects");
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto p-4">
        <div className="mx-auto max-w-2xl rounded-lg border border-strokedark bg-boxdark p-8">
          <div className="text-center">
            <div className="mb-4 flex justify-center">
              <div className="h-12 w-12 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
            </div>
            <p className="text-white">Verifying your payment...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto p-4">
        <div className="mx-auto max-w-2xl rounded-lg border border-strokedark bg-boxdark p-8">
          <div className="mb-8 text-center">
            <div className="mb-4 flex justify-center">
              <div className="rounded-full bg-danger/20 p-4">
                <svg
                  className="h-12 w-12 text-danger"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M6 18L18 6M6 6l12 12"
                  ></path>
                </svg>
              </div>
            </div>
            <h1 className="mb-2 text-3xl font-bold text-white">
              Payment Error
            </h1>
            <p className="text-lg text-bodydark">{error}</p>
          </div>
          <div className="text-center">
            <button
              onClick={handleViewInvoices}
              className="inline-flex items-center rounded-md bg-primary px-6 py-3 text-white hover:bg-opacity-90"
            >
              View Invoices
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4">
      <div className="mx-auto max-w-2xl rounded-lg border border-strokedark bg-boxdark p-8">
        <div className="mb-8 text-center">
          <div className="mb-4 flex justify-center">
            <div className="rounded-full bg-success/20 p-4">
              <svg
                className="h-12 w-12 text-success"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M5 13l4 4L19 7"
                ></path>
              </svg>
            </div>
          </div>
          <h1 className="mb-2 text-3xl font-bold text-white">
            Payment Successful!
          </h1>
          <p className="text-lg text-bodydark">
            Thank you for your payment. Your transaction has been completed
            successfully.
          </p>
        </div>

        <div className="mb-8 rounded-lg border border-stroke bg-meta-4/20 p-6">
          <h2 className="mb-4 text-xl font-semibold text-white">
            Payment Details
          </h2>
          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="text-bodydark">Session ID:</span>
              <span className="font-mono text-sm text-white">{sessionId}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-bodydark">Payment Method:</span>
              <span className="text-white">Stripe Checkout</span>
            </div>
            <div className="flex justify-between">
              <span className="text-bodydark">Status:</span>
              <span className="font-semibold text-success">Completed</span>
            </div>
          </div>
        </div>

        <div className="mb-8 rounded-lg border border-stroke bg-meta-4/20 p-6">
          <h3 className="mb-4 text-lg font-semibold text-white">
            What happens next?
          </h3>
          <ul className="space-y-2 text-bodydark">
            <li className="flex items-start">
              <span className="mr-2 text-success">•</span>
              You will receive a confirmation email shortly
            </li>
            <li className="flex items-start">
              <span className="mr-2 text-success">•</span>
              We'll begin processing your order right away
            </li>
            <li className="flex items-start">
              <span className="mr-2 text-success">•</span>
              You can track your order status in your dashboard
            </li>
          </ul>
        </div>

        <div className="flex flex-col justify-center gap-4 sm:flex-row">
          <button
            onClick={handleViewInvoices}
            className="inline-flex items-center justify-center rounded-md bg-primary px-6 py-3 text-white hover:bg-opacity-90"
          >
            {!authState.isLoggedIn || !authState.token
              ? "Login to View Invoices"
              : "View My Invoices"}
          </button>
          <button
            onClick={handleViewDashboard}
            className="inline-flex items-center justify-center rounded-md border border-stroke px-6 py-3 text-white hover:bg-meta-4"
          >
            {!authState.isLoggedIn || !authState.token
              ? "Login to Dashboard"
              : "Go to Dashboard"}
          </button>
        </div>

        <div className="mt-6 text-center">
          <p className="text-bodydark">
            If you have any questions about your payment or order, please{" "}
            <Link to="/contact" className="text-primary hover:underline">
              contact our support team
            </Link>
            .
          </p>
        </div>
      </div>
    </div>
  );
};

export default InvoiceClientSuccessPage;
