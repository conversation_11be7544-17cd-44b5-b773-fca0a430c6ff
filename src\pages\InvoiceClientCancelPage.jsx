import React, { useState, useEffect } from "react";
import { useSearchParams, useNavigate } from "react-router-dom";
import { GlobalContext, showToast } from "Src/globalContext";
import { AuthContext } from "Src/authContext";
import { X, AlertCircle } from "lucide-react";
import CustomModal from "Components/CustomModal";

const InvoiceClientCancelPage = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const { state: authState } = React.useContext(AuthContext);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [loading, setLoading] = useState(true);

  const invoiceId = searchParams.get("invoice_id");

  useEffect(() => {
    // Check if we have an invoice_id parameter
    if (invoiceId) {
      // Show cancel modal
      setIsModalOpen(true);
      showToast(
        globalDispatch,
        "Payment was cancelled. You can try again anytime.",
        5000,
        "warning"
      );
    } else {
      // If no invoice_id, redirect to login
      navigate("/login");
    }
    setLoading(false);
  }, [invoiceId, navigate, globalDispatch]);

  const handleModalClose = () => {
    setIsModalOpen(false);
    
    // Check if user is logged in
    if (!authState.isLoggedIn || !authState.token) {
      // If not logged in (public), just show modal - don't redirect anywhere specific
      // User can close modal and stay on this page or navigate manually
      return;
    } else {
      // If logged in, redirect to the specific invoice page
      navigate(`/client/invoice/${invoiceId}`);
    }
  };

  const handleTryAgain = () => {
    setIsModalOpen(false);
    
    // Check if user is logged in
    if (!authState.isLoggedIn || !authState.token) {
      // If not logged in (public), redirect to the public invoice link
      window.location.href = `/invoice/${invoiceId}`;
    } else {
      // If logged in, redirect to the specific invoice page
      navigate(`/client/invoice/${invoiceId}`);
    }
  };

  if (loading) {
    return (
      <div className="flex h-screen items-center justify-center">
        <div className="h-16 w-16 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4">
      <div className="flex h-screen flex-col items-center justify-center">
        <div className="text-center">
          <div className="mb-4 flex justify-center">
            <div className="rounded-full bg-warning/20 p-4">
              <AlertCircle className="h-16 w-16 text-warning" />
            </div>
          </div>
          <h1 className="mb-4 text-4xl font-bold text-warning">
            Payment Cancelled
          </h1>
          <p className="mb-6 text-lg text-bodydark">
            Your payment was cancelled. No charges were made to your account.
          </p>
          <div className="flex gap-4 justify-center">
            <button
              onClick={handleTryAgain}
              className="rounded bg-primary px-6 py-3 text-white hover:bg-opacity-90"
            >
              Try Again
            </button>
            <button
              onClick={handleModalClose}
              className="rounded border border-stroke px-6 py-3 text-white hover:bg-meta-4"
            >
              {!authState.isLoggedIn || !authState.token ? "Close" : "Back to Invoice"}
            </button>
          </div>
        </div>
      </div>

      <CustomModal
        isOpen={isModalOpen}
        onClose={handleModalClose}
        title="Payment Cancelled"
      >
        <div className="p-4 text-center">
          <div className="mb-4 flex justify-center">
            <div className="rounded-full bg-warning/20 p-3">
              <X className="h-12 w-12 text-warning" />
            </div>
          </div>
          <h3 className="mb-4 text-xl font-semibold text-white">
            Payment was cancelled
          </h3>
          <p className="mb-6 text-bodydark">
            No charges were made to your account. You can try the payment again anytime.
          </p>
          <div className="flex gap-3 justify-center">
            <button
              onClick={handleTryAgain}
              className="rounded bg-primary px-4 py-2 text-white hover:bg-opacity-90"
            >
              Try Again
            </button>
            <button
              onClick={handleModalClose}
              className="rounded border border-stroke px-4 py-2 text-white hover:bg-meta-4"
            >
              {!authState.isLoggedIn || !authState.token ? "Close" : "Back to Invoice"}
            </button>
          </div>
        </div>
      </CustomModal>
    </div>
  );
};

export default InvoiceClientCancelPage;
