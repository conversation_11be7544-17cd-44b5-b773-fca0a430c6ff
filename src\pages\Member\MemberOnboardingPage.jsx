import React, { useEffect, useState } from "react";
import { Link, useNavigate } from "react-router-dom";
import { AuthContext } from "Src/authContext";
import { GlobalContext, showToast } from "Src/globalContext";
import MemberSubscriptionPage from "./MemberSubscriptionPage";
import SubscriptionDetailsPage from "./SubscriptionDetailsPage";
import OnboardingSteps from "./OnboardingSteps";
import MkdSDK from "Utils/MkdSDK";
import { getUserDetailsByIdAPI } from "Src/services/userService";

const MemberOnboardingPage = () => {
  const navigate = useNavigate();
  const { dispatch: authDispatch } = React.useContext(AuthContext);
  const { dispatch: globalDispatch, state } = React.useContext(GlobalContext);

  // State for tracking onboarding flow
  const [onboardingStep, setOnboardingStep] = useState("plan-selection"); // 'plan-selection', 'plan-details', 'onboarding-steps'
  const [selectedPlan, setSelectedPlan] = useState(null);
  const [selectedPlanDetails, setSelectedPlanDetails] = useState(null);
  const [selectedInterval, setSelectedInterval] = useState("month");
  const [selectedProjectRange, setSelectedProjectRange] = useState("1-50");
  const [userDetails, setUserDetails] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isMainMember, setIsMainMember] = useState(true); // Default to true for safety

  const handleLogout = () => {
    authDispatch({ type: "LOGOUT" });
    localStorage.clear();
    navigate("/member/login");
  };

  // Handle plan selection
  const handlePlanSelected = (planId, planDetails, interval, projectRange) => {
    setSelectedPlan(planId);
    setSelectedPlanDetails(planDetails);
    setSelectedInterval(interval);
    setSelectedProjectRange(projectRange);
    setOnboardingStep("plan-details");
  };

  // Handle back to plan selection
  const handleBackToPlanSelection = () => {
    setOnboardingStep("plan-selection");
  };

  // Handle subscription creation
  const handleCreateSubscription = async (cardToken) => {
    setIsLoading(true);
    try {
      const sdk = new MkdSDK();

      // Create or update Stripe customer
      const customerParams = {
        cardToken: cardToken,
      };

      // Create customer if needed
      await sdk.createStripeCustomer(customerParams);

      // Create subscription
      const subscriptionResult = await sdk.createStripeSubscription({
        planId: selectedPlan,
      });

      if (!subscriptionResult.error) {
        showToast(
          globalDispatch,
          "Successfully subscribed to plan",
          4000,
          "success"
        );

        // Fetch user details for onboarding
        const userId = localStorage.getItem("user");
        const userResult = await getUserDetailsByIdAPI(userId);

        if (!userResult.error) {
          console.log("User details for onboarding:", userResult.model);

          // Check if onboarding is already complete
          let onboardingComplete = false;
          if (userResult.model.steps) {
            try {
              const stepData = JSON.parse(userResult.model.steps);
              onboardingComplete = stepData.onboarding_complete === true;
              console.log(
                "Onboarding status from user details:",
                onboardingComplete
              );
            } catch (e) {
              console.error("Error parsing step data:", e);
            }
          }

          if (onboardingComplete) {
            // If onboarding is already complete, go to dashboard
            navigate("/member/dashboard");
          } else {
            // Otherwise, proceed to onboarding steps
            setUserDetails(userResult.model);
            setOnboardingStep("onboarding-steps");
          }
        }
      } else {
        showToast(
          globalDispatch,
          "Failed to create subscription",
          4000,
          "error"
        );
      }
    } catch (error) {
      console.error("Error creating subscription:", error);
      showToast(
        globalDispatch,
        error.message || "Failed to create subscription",
        4000,
        "error"
      );
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "onboarding",
      },
    });

    // Check if user is a main member and handle sub-member flow
    const checkUserMembershipStatus = async () => {
      try {
        const userId = localStorage.getItem("user");
        if (userId) {
          const result = await getUserDetailsByIdAPI(Number(userId));
          if (!result.error && result.model) {
            setUserDetails(result.model);

            // Check if user is a main member (not a sub-member)
            const isMain =
              !result.model.main_user_details ||
              result.model.main_user_details.is_self === true;
            setIsMainMember(isMain);

            console.log("User details in onboarding:", result.model);
            console.log("Is main member:", isMain);

            // If user is not a main member, redirect them to dashboard
            if (!isMain) {
              console.log("Sub-member detected, redirecting to dashboard");
              navigate("/member/dashboard");
              return false; // Indicate that we should stop onboarding flow
            }

            return true; // Continue with onboarding flow
          }
        }
      } catch (error) {
        console.error("Error checking user membership status:", error);
      }
      return true; // Default to continuing if there's an error
    };

    const checkSubscription = async () => {
      // First check if user is a main member
      const canContinue = await checkUserMembershipStatus();
      if (!canContinue) {
        return; // Sub-member was redirected to dashboard
      }

      const sdk = new MkdSDK();
      const subscriptionResult = await sdk.getCustomerStripeSubscription();

      // If subscription exists, check user details for onboarding completion
      if (subscriptionResult.customer?.planId) {
        localStorage.setItem("subscribed", true);

        // Fetch user details to check onboarding status
        const userId = localStorage.getItem("user");
        const userResult = await getUserDetailsByIdAPI(userId);

        if (!userResult.error) {
          // Check if onboarding is complete by looking at the steps field
          let onboardingComplete = false;

          if (userResult.model.steps) {
            try {
              const stepData = JSON.parse(userResult.model.steps);
              onboardingComplete = stepData.onboarding_complete === true;
              console.log(
                "Onboarding status from user details:",
                onboardingComplete
              );
            } catch (e) {
              console.error("Error parsing step data:", e);
            }
          }

          if (onboardingComplete) {
            // If onboarding is complete, go to dashboard
            navigate("/member/dashboard");
            return;
          } else {
            // If onboarding is not complete, go to onboarding steps
            setUserDetails(userResult.model);
            setOnboardingStep("onboarding-steps");
            return;
          }
        }
      }

      // Otherwise, show plan selection
      localStorage.setItem("subscribed", "");
    };

    (async () => {
      await checkSubscription();
    })();
  }, [globalDispatch, navigate]);

  // Render content based on current onboarding step
  const renderContent = () => {
    switch (onboardingStep) {
      case "plan-selection":
        return (
          <MemberSubscriptionPage
            isOnboarding={true}
            onPlanSelected={handlePlanSelected}
          />
        );

      case "plan-details":
        return (
          <SubscriptionDetailsPage
            selectedPlan={selectedPlan}
            planDetails={selectedPlanDetails}
            onBack={handleBackToPlanSelection}
            onProceed={handleCreateSubscription}
            billingInterval={selectedInterval}
            projectRange={selectedProjectRange}
          />
        );

      case "onboarding-steps":
        return (
          <OnboardingSteps
            userDetails={userDetails}
            subscriptionType={selectedPlanDetails?.name || "The Portal"}
          />
        );

      default:
        return <MemberSubscriptionPage isOnboarding={true} />;
    }
  };

  return (
    <div className="p-4 mx-auto min-h-screen max-w-screen md:p-6 2xl:p-10">
      <div className="flex flex-col p-6 mb-8 rounded-lg bg-boxdark">
        <div className="flex justify-between items-center mb-8">
          <Link className="inline-block" to="/">
            <img
              crossOrigin="anonymous"
              src={
                state.siteLogo ??
                `${window.location.origin}/new/cheerEQ-2-Ed2.png`
              }
              className="h-auto w-[300px] dark:hidden"
              alt="Logo"
            />
          </Link>
          <div className="flex gap-4">
            <Link
              to="/"
              className="inline-flex items-center justify-center rounded-md border border-stroke px-6 py-2.5 text-center font-medium text-white hover:bg-opacity-90"
            >
              Return Home
            </Link>
            <button
              onClick={handleLogout}
              className="inline-flex items-center justify-center rounded-md bg-danger px-6 py-2.5 text-center font-medium text-white hover:bg-opacity-90"
            >
              Logout
            </button>
          </div>
        </div>

        {onboardingStep === "plan-selection" && (
          <div className="flex flex-col justify-center items-center">
            <h1 className="mb-4 text-3xl font-bold text-white">
              Hello, {localStorage.getItem("userName")}
            </h1>
            <h2 className="mb-4 text-2xl font-bold text-white">
              Welcome to Equality Records!
            </h2>
            <p className="mb-6 text-lg text-bodydark">
              To get started, please select a subscription plan that best fits
              your needs.
            </p>
          </div>
        )}
      </div>

      {isLoading ? (
        <div className="flex justify-center items-center h-64">
          <div className="w-16 h-16 rounded-full border-t-4 border-b-4 animate-spin border-primary"></div>
        </div>
      ) : (
        renderContent()
      )}
    </div>
  );
};

export default MemberOnboardingPage;
