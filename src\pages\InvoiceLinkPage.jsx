import React, { useState, useEffect } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { loadStripe } from "@stripe/stripe-js";
import { Elements, useStripe, useElements } from "@stripe/react-stripe-js";
import InvoiceMultiStep, { STEPS } from "Components/Invoice/InvoiceMultiStep";
import MkdSDK from "Utils/MkdSDK";
import { GlobalContext, showToast } from "Src/globalContext";
import SuccessModal from "Components/SuccessModal";

const stripePromise = loadStripe(
  "pk_test_51Ll5ukBgOlWo0lDUrBhA2W7EX2MwUH9AR5Y3KQoujf7PTQagZAJylWP1UOFbtH4UwxoufZbInwehQppWAq53kmNC00UIKSmebO"
);

const InvoiceLinkPage = () => {
  const params = useParams();
  const invoiceId = params.invoiceId;
  const token = params.token;
  const navigate = useNavigate();
  const [invoiceData, setInvoiceData] = useState(null);
  const [clientData, setClientData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const [step2Info, setStep2Info] = useState({
    feedback_notes: "",
    signature: "",
    initial: "",
  });
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [legalDocuments, setLegalDocuments] = useState({
    termsOfService: "",
    privacyPolicy: "",
  });

  const stripe = useStripe();
  const elements = useElements();

  const fetchClientDetails = async (clientId) => {
    try {
      const sdk = new MkdSDK();
      const response = await sdk.callRawAPI(
        `/v3/api/custom/equality_record/client/${clientId}`,
        [],
        "GET"
      );

      if (!response.error) {
        setClientData(response?.model);
      } else {
        console.error("Error fetching client details:", response.message);
      }
    } catch (err) {
      console.error("Error fetching client details:", err);
    }
  };

  const fetchLegalDocuments = async () => {
    try {
      const sdk = new MkdSDK();
      const response = await sdk.callRawAPI(
        "/v3/api/custom/equality_record/user/legal-documents",
        [],
        "GET"
      );

      if (!response.error && response.data && response.data.length > 0) {
        const legalDoc = response.data[0];
        setLegalDocuments({
          termsOfService: legalDoc.terms_of_service || "",
          privacyPolicy: legalDoc.privacy_policy || "",
        });
      } else {
        console.error("Error fetching legal documents:", response.message);
      }
    } catch (err) {
      console.error("Error fetching legal documents:", err);
    }
  };

  console.log(invoiceData);

  useEffect(() => {
    const fetchInvoice = async () => {
      try {
        const sdk = new MkdSDK();
        const response = await sdk.callRawAPI(
          `/v3/api/custom/equality_record/subscription/public/invoice/${invoiceId}/${token}`,
          [],
          "GET"
        );

        if (response.error) {
          setError("Invalid or expired link");
          showToast(globalDispatch, "Invalid or expired link", "error");
        } else {
          setInvoiceData(response);
          setStep2Info((prev) => ({
            ...prev,
            feedback_notes: response?.invoice?.feedback_notes,
            signature: response?.invoice?.signature,
            initial: response?.invoice?.initials,
          }));

          if (response?.invoice?.client_id) {
            await fetchClientDetails(response?.invoice?.client_id);
          }

          // Fetch legal documents
          await fetchLegalDocuments();
        }
      } catch (err) {
        console.error("Error fetching invoice:", err);
        setError("Failed to load invoice");
        showToast(globalDispatch, "Failed to load invoice", "error");
      } finally {
        setLoading(false);
      }
    };

    if (invoiceId && token) {
      fetchInvoice();
    } else {
      setError("Invalid invoice link");
      setLoading(false);
    }
  }, [invoiceId, token, isModalOpen, globalDispatch]);

  const updateClientDetails = async (clientId, clientData) => {
    try {
      const sdk = new MkdSDK();
      const response = await sdk.callRawAPI(
        `/v3/api/custom/equality_record/client/${clientId}`,
        clientData,
        "PUT"
      );

      if (!response.error) {
        showToast(globalDispatch, "Client details updated successfully");
        return true;
      } else {
        showToast(globalDispatch, "Failed to update client details", "error");
        return false;
      }
    } catch (err) {
      showToast(globalDispatch, "Failed to update client details", "error");
      return false;
    }
  };

  const handleMultiStepComplete = async (data) => {
    try {
      const sdk = new MkdSDK();

      // Handle client details update
      if (data.step === STEPS.CLIENT_DETAILS) {
        if (data.clientDetails && invoiceData?.invoice?.client_id) {
          const clientUpdateSuccess = await updateClientDetails(
            invoiceData.invoice.client_id,
            data.clientDetails
          );
          if (!clientUpdateSuccess) {
            return;
          }
        }
        return;
      }

      // Handle signature and service agreement update
      if (data.step === STEPS.SIGN_REVIEW_SERVICE) {
        if (data.signature || data.initial || data.feedback) {
          await sdk.callRawAPI(
            `/v3/api/custom/equality_record/subscription/public/invoice/${invoiceId}/${token}`,
            {
              signature: data.signature,
              initial: data.initial,
              feedback_notes: data.feedback,
            },
            "PUT"
          );
        }
        return;
      }

      if (data.type === "payment") {
        console.log("data.payload", data.payload);

        try {
          // Get the producer name from the first item's producers array if available
          let producerName = "";
          if (
            invoiceData?.items?.length > 0 &&
            invoiceData.items[0].producers?.length > 0
          ) {
            producerName = invoiceData.items[0].producers[0];
          }

          // Update the payload to include the producer name
          const updatedPayload = {
            ...data.payload,
            producerName: producerName,
          };

          // Handle payment based on payment method
          if (data.payload.paymentMethod === "checkout") {
            // Process Stripe Checkout payment
            const response = await sdk.callRawAPI(
              `/v3/api/custom/equality_record/subscription/public/payment`,
              updatedPayload,
              "POST"
            );

            if (response.error) {
              showToast(
                globalDispatch,
                "Payment failed: " + response.message,
                "error"
              );
              return;
            }

            // Check if we have the required checkout URL
            if (!response.stripe_checkout_url) {
              showToast(
                globalDispatch,
                "Failed to create checkout session - missing checkout URL",
                "error"
              );
              return;
            }

            // Show payment in progress message with countdown
            showToast(
              globalDispatch,
              "Payment in progress! Redirecting to Stripe checkout...",
              6000,
              "success"
            );

            // Redirect to Stripe checkout after 6 seconds
            setTimeout(() => {
              window.location.href = response.stripe_checkout_url;
            }, 6000);

            return; // Don't continue with the rest of the flow
          } else if (data.payload.paymentMethod === "check") {
            // Process check payment - this is simpler as it just records the payment intent
            const response = await sdk.callRawAPI(
              `/v3/api/custom/equality_record/subscription/public/payment`,
              updatedPayload,
              "POST"
            );

            if (response.error) {
              showToast(
                globalDispatch,
                "Payment failed: " + response.message,
                "error"
              );
              return;
            }
          }

          showToast(globalDispatch, "Payment successful", 4000, "success");
          setIsModalOpen(true);
        } catch (err) {
          console.error("Payment processing error:", err);
          showToast(
            globalDispatch,
            "Payment failed. Please try again.",
            4000,
            "error"
          );
          return;
        }
      }
    } catch (err) {
      console.error("Error processing request:", err);
      showToast(
        globalDispatch,
        "An error occurred. Please try again.",
        4000,
        "error"
      );
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <div className="w-16 h-16 rounded-full border-4 animate-spin border-primary border-t-transparent"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col justify-center items-center h-screen">
        <h1 className="mb-4 text-2xl font-bold text-danger">{error}</h1>
        <p className="text-bodydark">
          Please contact support if you believe this is an error.
        </p>
      </div>
    );
  }

  return (
    <div className="container p-4 mx-auto">
      <Elements stripe={stripePromise}>
        <InvoiceMultiStep
          step2Info={step2Info}
          setStep2Info={setStep2Info}
          invoiceData={invoiceData}
          onSubmit={handleMultiStepComplete}
          termsContent={invoiceData?.invoice?.terms_and_conditions || ""}
          serviceAgreementContent={
            invoiceData?.invoice?.terms_and_conditions || ""
          }
          token={token}
          clientData={clientData}
          companyInfo={invoiceData?.company_info}
          legalDocuments={legalDocuments}
        />
      </Elements>
      <SuccessModal
        isOpen={isModalOpen}
        onClose={() => {
          // Redirect to a thank you page or close the window after successful payment
          window.location.href = "https://equalityrecords.com/client/login";
        }}
        message="Your payment was successful!. Login to your account to access your projects"
      />
    </div>
  );
};

export default InvoiceLinkPage;
